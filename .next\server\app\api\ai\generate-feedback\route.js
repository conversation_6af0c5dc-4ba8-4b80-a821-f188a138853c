/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/generate-feedback/route";
exports.ids = ["app/api/ai/generate-feedback/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "postgres":
/*!***************************!*\
  !*** external "postgres" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("postgres");;

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-feedback%2Froute&page=%2Fapi%2Fai%2Fgenerate-feedback%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-feedback%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-feedback%2Froute&page=%2Fapi%2Fai%2Fgenerate-feedback%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-feedback%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_ai_generate_feedback_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/generate-feedback/route.ts */ \"(rsc)/./src/app/api/ai/generate-feedback/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_ai_generate_feedback_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_ai_generate_feedback_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/generate-feedback/route\",\n        pathname: \"/api/ai/generate-feedback\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/generate-feedback/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\api\\\\ai\\\\generate-feedback\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_ai_generate_feedback_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-feedback%2Froute&page=%2Fapi%2Fai%2Fgenerate-feedback%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-feedback%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/generate-feedback/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/ai/generate-feedback/route.ts ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_ai_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/ai-service */ \"(rsc)/./src/lib/ai-service.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_auth__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nasync function POST(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { resultId, scores } = await request.json();\n        if (!resultId || !scores) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Result ID and scores are required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate scores\n        const { listening, reading, writing, speaking, overall } = scores;\n        if (!overall) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Overall band score is required for feedback generation'\n            }, {\n                status: 400\n            });\n        }\n        // Generate AI feedback\n        const feedback = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_2__.generatePersonalizedFeedback)({\n            listeningScore: listening,\n            readingScore: reading,\n            writingScore: writing,\n            speakingScore: speaking,\n            overallScore: overall\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            feedback,\n            resultId\n        });\n    } catch (error) {\n        console.error('Error generating AI feedback:', error);\n        // Handle specific AI service errors\n        if (error instanceof Error) {\n            if (error.message.includes('API key')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'AI service configuration error. Please contact administrator.'\n                }, {\n                    status: 500\n                });\n            }\n            if (error.message.includes('rate limit')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'AI service temporarily unavailable. Please try again later.'\n                }, {\n                    status: 429\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate feedback. Please try again.'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/generate-feedback/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ai-service.ts":
/*!*******************************!*\
  !*** ./src/lib/ai-service.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateAIFeedback: () => (/* binding */ generateAIFeedback),\n/* harmony export */   generatePersonalizedFeedback: () => (/* binding */ generatePersonalizedFeedback)\n/* harmony export */ });\n/* harmony import */ var _anthropic_ai_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @anthropic-ai/sdk */ \"(rsc)/./node_modules/@anthropic-ai/sdk/index.mjs\");\n\nconst anthropic = new _anthropic_ai_sdk__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.ANTHROPIC_API_KEY\n});\nasync function generateAIFeedback(testResult, candidate) {\n    const prompt = `\nYou are an expert IELTS examiner and English language learning advisor. Please provide comprehensive feedback for the following IELTS test results:\n\nCandidate Information:\n- Name: ${candidate.fullName}\n- Nationality: ${candidate.nationality}\n- Test Date: ${candidate.testDate}\n\nTest Scores:\n- Listening: ${testResult.listeningBandScore}/9.0 (Raw: ${testResult.listeningScore})\n- Reading: ${testResult.readingBandScore}/9.0 (Raw: ${testResult.readingScore})\n- Writing: ${testResult.writingBandScore}/9.0 (Task 1: ${testResult.writingTask1Score}, Task 2: ${testResult.writingTask2Score})\n- Speaking: ${testResult.speakingBandScore}/9.0 (Fluency: ${testResult.speakingFluencyScore}, Lexical: ${testResult.speakingLexicalScore}, Grammar: ${testResult.speakingGrammarScore}, Pronunciation: ${testResult.speakingPronunciationScore})\n- Overall Band Score: ${testResult.overallBandScore}/9.0\n\nPlease provide:\n\n1. Detailed feedback for each skill (Listening, Reading, Writing, Speaking) - 2-3 sentences each\n2. Overall performance feedback - 3-4 sentences\n3. Specific recommendations for improvement - 4-5 actionable points\n4. List of 3-4 key strengths\n5. List of 3-4 areas for improvement\n6. A personalized study plan including:\n   - Recommended timeframe for improvement\n   - 3-4 focus areas\n   - 4-5 specific resources\n   - 5-6 practice activities\n\nFormat your response as JSON with the following structure:\n{\n  \"listeningFeedback\": \"...\",\n  \"readingFeedback\": \"...\",\n  \"writingFeedback\": \"...\",\n  \"speakingFeedback\": \"...\",\n  \"overallFeedback\": \"...\",\n  \"recommendations\": \"...\",\n  \"strengths\": [\"...\", \"...\", \"...\"],\n  \"weaknesses\": [\"...\", \"...\", \"...\"],\n  \"studyPlan\": {\n    \"timeframe\": \"...\",\n    \"focusAreas\": [\"...\", \"...\", \"...\"],\n    \"resources\": [\"...\", \"...\", \"...\", \"...\"],\n    \"practiceActivities\": [\"...\", \"...\", \"...\", \"...\", \"...\"]\n  }\n}\n\nMake the feedback constructive, specific, and encouraging. Focus on practical advice that the candidate can implement.\n`;\n    try {\n        const response = await anthropic.messages.create({\n            model: 'claude-3-sonnet-20240229',\n            max_tokens: 2000,\n            messages: [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ]\n        });\n        const content = response.content[0];\n        if (content.type !== 'text') {\n            throw new Error('Unexpected response type from Claude');\n        }\n        // Parse the JSON response\n        const feedbackData = JSON.parse(content.text);\n        return feedbackData;\n    } catch (error) {\n        console.error('Error generating AI feedback:', error);\n        // Return fallback feedback if AI service fails\n        return {\n            listeningFeedback: `With a band score of ${testResult.listeningBandScore}, your listening skills show good comprehension abilities. Continue practicing with various accents and audio materials.`,\n            readingFeedback: `Your reading score of ${testResult.readingBandScore} indicates solid reading comprehension. Focus on improving speed and accuracy with different text types.`,\n            writingFeedback: `Your writing band score of ${testResult.writingBandScore} shows competent writing skills. Work on task achievement, coherence, and lexical resource.`,\n            speakingFeedback: `With a speaking score of ${testResult.speakingBandScore}, you demonstrate good oral communication. Continue practicing fluency and pronunciation.`,\n            overallFeedback: `Your overall band score of ${testResult.overallBandScore} reflects your current English proficiency level. With focused practice, you can improve your performance across all skills.`,\n            recommendations: 'Practice regularly with authentic IELTS materials, focus on your weaker skills, and consider taking a preparation course.',\n            strengths: [\n                'Good overall comprehension',\n                'Adequate vocabulary range',\n                'Basic grammar understanding'\n            ],\n            weaknesses: [\n                'Need more practice with complex structures',\n                'Improve accuracy',\n                'Enhance fluency'\n            ],\n            studyPlan: {\n                timeframe: '3-6 months of focused study',\n                focusAreas: [\n                    'Grammar accuracy',\n                    'Vocabulary expansion',\n                    'Test strategies'\n                ],\n                resources: [\n                    'Official IELTS materials',\n                    'Cambridge IELTS books',\n                    'Online practice tests',\n                    'English news websites'\n                ],\n                practiceActivities: [\n                    'Daily reading practice',\n                    'Speaking with native speakers',\n                    'Writing essays weekly',\n                    'Listening to podcasts',\n                    'Taking mock tests'\n                ]\n            }\n        };\n    }\n}\n// New function for the feedback API\nasync function generatePersonalizedFeedback(request) {\n    const { overallScore, listeningScore, readingScore, writingScore, speakingScore } = request;\n    // Check if Claude API key is available\n    const apiKey = process.env.ANTHROPIC_API_KEY;\n    if (!apiKey) {\n        console.warn('ANTHROPIC_API_KEY not found, using mock feedback');\n        return generateMockFeedback(request);\n    }\n    try {\n        // Prepare the prompt for Claude\n        const prompt = `You are an expert IELTS examiner and English language teacher. Generate personalized feedback for a student based on their IELTS test scores.\n\nTest Scores:\n- Overall Band Score: ${overallScore}\n- Listening: ${listeningScore || 'Not provided'}\n- Reading: ${readingScore || 'Not provided'}\n- Writing: ${writingScore || 'Not provided'}\n- Speaking: ${speakingScore || 'Not provided'}\n\nPlease provide detailed feedback in the following JSON format:\n{\n  \"overallAssessment\": \"A comprehensive assessment of the student's English proficiency level\",\n  \"strengths\": [\"List of 3-4 specific strengths based on the scores\"],\n  \"areasForImprovement\": [\"List of 3-4 specific areas that need improvement\"],\n  \"specificRecommendations\": {\n    \"listening\": \"Specific advice for improving listening skills\",\n    \"reading\": \"Specific advice for improving reading skills\",\n    \"writing\": \"Specific advice for improving writing skills\",\n    \"speaking\": \"Specific advice for improving speaking skills\"\n  },\n  \"studyPlan\": \"A detailed study plan recommendation based on the current level\",\n  \"nextSteps\": [\"List of 4-5 actionable next steps for improvement\"]\n}\n\nMake the feedback encouraging but honest, specific to the scores provided, and actionable. Consider the IELTS band descriptors when providing recommendations.`;\n        const response = await anthropic.messages.create({\n            model: 'claude-3-sonnet-20240229',\n            max_tokens: 2000,\n            messages: [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ]\n        });\n        const content = response.content[0];\n        if (content.type !== 'text') {\n            throw new Error('Unexpected response type from Claude');\n        }\n        // Parse the JSON response\n        try {\n            const feedback = JSON.parse(content.text);\n            return feedback;\n        } catch (err) {\n            console.error('Failed to parse Claude response:', content.text, err);\n            throw new Error('Invalid response format from AI service');\n        }\n    } catch (error) {\n        console.error('Claude API error:', error);\n        // Fallback to mock feedback if API fails\n        return generateMockFeedback(request);\n    }\n}\nfunction generateMockFeedback(request) {\n    const { overallScore } = request;\n    // Mock feedback based on score ranges\n    if (overallScore >= 7.0) {\n        return {\n            overallAssessment: \"Excellent performance! You have demonstrated strong English proficiency across all skills. Your overall band score of \" + overallScore + \" indicates you are a competent user of English with good operational command of the language.\",\n            strengths: [\n                \"Strong overall command of English language\",\n                \"Good vocabulary range and accuracy in most contexts\",\n                \"Effective communication skills with minor inaccuracies\",\n                \"Ability to handle complex language situations\"\n            ],\n            areasForImprovement: [\n                \"Fine-tune advanced grammar structures for academic contexts\",\n                \"Expand specialized academic and professional vocabulary\",\n                \"Practice complex sentence formations and cohesive devices\",\n                \"Work on consistency across all four skills\"\n            ],\n            specificRecommendations: {\n                listening: \"Focus on academic lectures, complex discussions, and various English accents. Practice note-taking while listening to improve comprehension of detailed information.\",\n                reading: \"Practice with academic texts, research papers, and complex argumentative essays. Work on speed reading techniques while maintaining comprehension.\",\n                writing: \"Work on advanced essay structures, sophisticated argumentation, and academic writing conventions. Focus on task achievement and coherence.\",\n                speaking: \"Practice formal presentations, debates, and discussions on abstract topics. Work on fluency and natural expression of complex ideas.\"\n            },\n            studyPlan: \"Continue with advanced materials focusing on academic English. Dedicate 2-3 hours daily to practice, with emphasis on maintaining consistency across all skills. Use authentic materials like academic journals, TED talks, and formal debates.\",\n            nextSteps: [\n                \"Take regular practice tests to maintain performance level\",\n                \"Focus on any weaker skills to achieve balance across all areas\",\n                \"Consider advanced English courses or academic preparation programs\",\n                \"Practice with time constraints to improve efficiency\",\n                \"Engage with native speakers in academic or professional contexts\"\n            ]\n        };\n    } else if (overallScore >= 5.5) {\n        return {\n            overallAssessment: \"Good foundation with room for improvement in specific areas. Your overall band score of \" + overallScore + \" shows you are a modest user of English with partial command of the language, coping with overall meaning in most situations.\",\n            strengths: [\n                \"Basic communication skills are well-established\",\n                \"Understanding of fundamental grammar structures\",\n                \"Adequate vocabulary for everyday and familiar situations\",\n                \"Ability to express basic ideas and opinions clearly\"\n            ],\n            areasForImprovement: [\n                \"Expand vocabulary range for academic and professional contexts\",\n                \"Improve complex grammar usage and sentence structures\",\n                \"Enhance fluency and coherence in extended discourse\",\n                \"Develop better accuracy in language use\"\n            ],\n            specificRecommendations: {\n                listening: \"Practice with various accents, speeds, and contexts. Focus on understanding main ideas and specific details in academic and social situations.\",\n                reading: \"Work on skimming and scanning techniques. Practice with longer texts and improve vocabulary through extensive reading.\",\n                writing: \"Focus on paragraph structure, linking words, and task response. Practice both formal and informal writing styles with attention to coherence.\",\n                speaking: \"Practice pronunciation, intonation, and natural speech patterns. Work on expressing ideas clearly and developing responses fully.\"\n            },\n            studyPlan: \"Structured study plan focusing on intermediate to upper-intermediate materials. Dedicate 1-2 hours daily with balanced practice across all four skills. Use IELTS preparation materials and general English improvement resources.\",\n            nextSteps: [\n                \"Daily practice with all four skills using varied materials\",\n                \"Join English conversation groups or language exchange programs\",\n                \"Use IELTS preparation books and online resources systematically\",\n                \"Focus on building vocabulary through reading and listening\",\n                \"Take regular practice tests to track improvement\"\n            ]\n        };\n    } else {\n        return {\n            overallAssessment: \"Foundation level with significant room for improvement across all skills. Your overall band score of \" + overallScore + \" indicates limited user level, with basic competence limited to familiar situations and frequent communication breakdowns.\",\n            strengths: [\n                \"Basic understanding of English structure and patterns\",\n                \"Willingness to communicate despite limitations\",\n                \"Some vocabulary knowledge for familiar topics\",\n                \"Ability to convey basic information in simple situations\"\n            ],\n            areasForImprovement: [\n                \"Build fundamental vocabulary for everyday situations\",\n                \"Strengthen basic grammar and sentence construction\",\n                \"Improve listening comprehension for simple conversations\",\n                \"Develop basic writing skills and paragraph organization\"\n            ],\n            specificRecommendations: {\n                listening: \"Start with simple conversations, basic instructions, and familiar topics. Use visual aids and context clues to support understanding.\",\n                reading: \"Begin with short, simple texts on familiar topics. Focus on building sight vocabulary and basic comprehension skills.\",\n                writing: \"Focus on basic sentence structure, simple paragraphs, and essential grammar patterns. Practice writing about familiar topics.\",\n                speaking: \"Practice basic conversations, pronunciation of common words, and expressing simple ideas clearly and confidently.\"\n            },\n            studyPlan: \"Intensive foundation course focusing on basic English skills. Dedicate 1-2 hours daily to structured learning with emphasis on building confidence and fundamental skills. Use beginner-level materials and seek guidance from qualified teachers.\",\n            nextSteps: [\n                \"Enroll in a basic English course with qualified instruction\",\n                \"Practice daily with simple, structured materials\",\n                \"Focus on building confidence through successful communication\",\n                \"Use visual and audio aids to support learning\",\n                \"Set small, achievable goals to maintain motivation\"\n            ]\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ai-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_db__WEBPACK_IMPORTED_MODULE_2__]);\n_db__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst { handlers, auth, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    session: {\n        strategy: 'jwt'\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    // Find user in database\n                    const foundUser = await _db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users.email, credentials.email)).limit(1);\n                    if (foundUser.length === 0) {\n                        return null;\n                    }\n                    const user = foundUser[0];\n                    // Check password\n                    if (!user.password) {\n                        return null;\n                    }\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        return null;\n                    }\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Authentication error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n                token.email = user.email;\n                token.name = user.name;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.email = token.email;\n                session.user.name = token.name;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accounts),\n/* harmony export */   aiFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedback),\n/* harmony export */   candidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidates),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   sessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.sessions),\n/* harmony export */   testResults: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResults),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users),\n/* harmony export */   verificationTokens: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"postgres\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__]);\n([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst connectionString = process.env.DATABASE_URL;\n// Disable prefetch as it is not supported for \"Transaction\" pool mode\nconst client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n    prepare: false\n});\nconst db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n    schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n});\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWtEO0FBQ2xCO0FBQ0c7QUFFbkMsTUFBTUcsbUJBQW1CQyxRQUFRQyxHQUFHLENBQUNDLFlBQVk7QUFFakQsc0VBQXNFO0FBQ3RFLE1BQU1DLFNBQVNOLG9EQUFRQSxDQUFDRSxrQkFBa0I7SUFBRUssU0FBUztBQUFNO0FBQ3BELE1BQU1DLEtBQUtULGdFQUFPQSxDQUFDTyxRQUFRO0lBQUVMLE1BQU1BLHNDQUFBQTtBQUFDLEdBQUc7QUFFckIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRGVza3RvcFxcY29kZXNcXElFTFRTLUNlcnRpZmljYXRpb24tU3lzdGVtXFxzcmNcXGxpYlxcZGJcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRyaXp6bGUgfSBmcm9tICdkcml6emxlLW9ybS9wb3N0Z3Jlcy1qcyc7XG5pbXBvcnQgcG9zdGdyZXMgZnJvbSAncG9zdGdyZXMnO1xuaW1wb3J0ICogYXMgc2NoZW1hIGZyb20gJy4vc2NoZW1hJztcblxuY29uc3QgY29ubmVjdGlvblN0cmluZyA9IHByb2Nlc3MuZW52LkRBVEFCQVNFX1VSTCE7XG5cbi8vIERpc2FibGUgcHJlZmV0Y2ggYXMgaXQgaXMgbm90IHN1cHBvcnRlZCBmb3IgXCJUcmFuc2FjdGlvblwiIHBvb2wgbW9kZVxuY29uc3QgY2xpZW50ID0gcG9zdGdyZXMoY29ubmVjdGlvblN0cmluZywgeyBwcmVwYXJlOiBmYWxzZSB9KTtcbmV4cG9ydCBjb25zdCBkYiA9IGRyaXp6bGUoY2xpZW50LCB7IHNjaGVtYSB9KTtcblxuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWEnO1xuIl0sIm5hbWVzIjpbImRyaXp6bGUiLCJwb3N0Z3JlcyIsInNjaGVtYSIsImNvbm5lY3Rpb25TdHJpbmciLCJwcm9jZXNzIiwiZW52IiwiREFUQUJBU0VfVVJMIiwiY2xpZW50IiwicHJlcGFyZSIsImRiIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* binding */ accounts),\n/* harmony export */   aiFeedback: () => (/* binding */ aiFeedback),\n/* harmony export */   candidates: () => (/* binding */ candidates),\n/* harmony export */   sessions: () => (/* binding */ sessions),\n/* harmony export */   testResults: () => (/* binding */ testResults),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   verificationTokens: () => (/* binding */ verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/numeric.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/json.js\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(rsc)/./node_modules/@paralleldrive/cuid2/index.js\");\n\n\n// Users table for authentication\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    emailVerified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('emailVerified', {\n        mode: 'date'\n    }),\n    image: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('image'),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('password'),\n    role: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('role', {\n        enum: [\n            'admin',\n            'test_checker'\n        ]\n    }).notNull().default('test_checker'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Accounts table for OAuth\nconst accounts = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('accounts', {\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('type').notNull(),\n    provider: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('provider').notNull(),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('providerAccountId').notNull(),\n    refresh_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('refresh_token'),\n    access_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('access_token'),\n    expires_at: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.integer)('expires_at'),\n    token_type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token_type'),\n    scope: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('scope'),\n    id_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id_token'),\n    session_state: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_state')\n});\n// Sessions table for authentication\nconst sessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('sessions', {\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('sessionToken').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Verification tokens\nconst verificationTokens = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('verificationTokens', {\n    identifier: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('identifier').notNull(),\n    token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token').notNull(),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Candidates table\nconst candidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_number').notNull().unique(),\n    fullName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('full_name').notNull(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    phoneNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('phone_number').notNull(),\n    dateOfBirth: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('date_of_birth', {\n        mode: 'date'\n    }).notNull(),\n    nationality: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('nationality').notNull(),\n    passportNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('passport_number').notNull().unique(),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }).notNull(),\n    testCenter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_center').notNull(),\n    photoUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_url'),\n    photoData: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_data'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Test results table\nconst testResults = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_results', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').notNull().references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }),\n    // Listening scores\n    listeningScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('listening_score', {\n        precision: 3,\n        scale: 1\n    }),\n    listeningBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('listening_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Reading scores\n    readingScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('reading_score', {\n        precision: 3,\n        scale: 1\n    }),\n    readingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('reading_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Writing scores\n    writingTask1Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_task1_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingTask2Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_task2_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Speaking scores\n    speakingFluencyScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_fluency_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingLexicalScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_lexical_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingGrammarScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_grammar_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingPronunciationScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_pronunciation_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Overall score\n    overallBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('overall_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Status and metadata\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'pending',\n            'completed',\n            'verified'\n        ]\n    }).notNull().default('pending'),\n    enteredBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('entered_by').references(()=>users.id),\n    verifiedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('verified_by').references(()=>users.id),\n    certificateGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('certificate_generated').default(false),\n    certificateSerial: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_serial').unique(),\n    certificateUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_url'),\n    aiFeedbackGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('ai_feedback_generated').default(false),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// AI feedback table\nconst aiFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('ai_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testResultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_result_id').notNull().references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }),\n    // Feedback for each skill\n    listeningFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('listening_feedback'),\n    readingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reading_feedback'),\n    writingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('writing_feedback'),\n    speakingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('speaking_feedback'),\n    // Overall feedback and recommendations\n    overallFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('overall_feedback'),\n    studyRecommendations: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('study_recommendations'),\n    // Strengths and weaknesses\n    strengths: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('strengths').$type(),\n    weaknesses: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('weaknesses').$type(),\n    // Study plan suggestions\n    studyPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('study_plan').$type(),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('generated_at').defaultNow().notNull()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/drizzle-orm","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/bcryptjs","vendor-chunks/@noble","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@paralleldrive","vendor-chunks/@panva","vendor-chunks/@anthropic-ai"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-feedback%2Froute&page=%2Fapi%2Fai%2Fgenerate-feedback%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-feedback%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();