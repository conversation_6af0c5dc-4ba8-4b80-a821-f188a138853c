import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { testResults, candidates } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data = await request.json();

    // Validate required fields
    if (!data.candidateId) {
      return NextResponse.json(
        { error: 'Candidate ID is required' },
        { status: 400 }
      );
    }

    // Check if candidate exists
    const candidate = await db
      .select()
      .from(candidates)
      .where(eq(candidates.id, data.candidateId))
      .limit(1);

    if (!candidate.length) {
      return NextResponse.json(
        { error: 'Candidate not found' },
        { status: 404 }
      );
    }

    // Check if results already exist for this candidate
    const existingResults = await db
      .select()
      .from(testResults)
      .where(eq(testResults.candidateId, data.candidateId))
      .limit(1);

    const isUpdate = existingResults.length > 0;

    // Convert string values to numbers where needed
    const processedData = {
      candidateId: data.candidateId,

      // Listening scores (keep as strings for decimal fields)
      listeningScore: data.listeningScore || null,
      listeningBandScore: data.listeningBandScore || null,

      // Reading scores
      readingScore: data.readingScore || null,
      readingBandScore: data.readingBandScore || null,

      // Writing scores
      writingTask1Score: data.writingTask1Score || null,
      writingTask2Score: data.writingTask2Score || null,
      writingBandScore: data.writingBandScore || null,

      // Speaking scores
      speakingFluencyScore: data.speakingFluencyScore || null,
      speakingLexicalScore: data.speakingLexicalScore || null,
      speakingGrammarScore: data.speakingGrammarScore || null,
      speakingPronunciationScore: data.speakingPronunciationScore || null,
      speakingBandScore: data.speakingBandScore || null,

      // Overall score
      overallBandScore: data.overallBandScore || null,

      // Metadata
      status: data.status || 'pending' as const,
      enteredBy: session.user?.id,
      updatedAt: new Date(),
    };

    let result;
    if (isUpdate) {
      // Update existing result
      result = await db
        .update(testResults)
        .set(processedData)
        .where(eq(testResults.id, existingResults[0].id))
        .returning();
    } else {
      // Create new test result
      result = await db
        .insert(testResults)
        .values(processedData)
        .returning();
    }

    return NextResponse.json(result[0], { status: isUpdate ? 200 : 201 });
  } catch (error) {
    console.error('Error creating test result:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status');

    const offset = (page - 1) * limit;
    const userId = session.user?.id;

    // Build where conditions
    let whereConditions = eq(testResults.enteredBy, userId);
    if (status) {
      whereConditions = eq(testResults.enteredBy, userId);
      // Add status filter if needed
    }

    // Get results with candidate info
    const results = await db
      .select({
        id: testResults.id,
        candidateId: testResults.candidateId,
        listeningBandScore: testResults.listeningBandScore,
        readingBandScore: testResults.readingBandScore,
        writingBandScore: testResults.writingBandScore,
        speakingBandScore: testResults.speakingBandScore,
        overallBandScore: testResults.overallBandScore,
        status: testResults.status,
        createdAt: testResults.createdAt,
        candidate: {
          fullName: candidates.fullName,
          passportNumber: candidates.passportNumber,
          testDate: candidates.testDate,
        },
      })
      .from(testResults)
      .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
      .where(whereConditions)
      .limit(limit)
      .offset(offset)
      .orderBy(testResults.createdAt);

    return NextResponse.json({
      results,
      page,
      limit,
    });
  } catch (error) {
    console.error('Error fetching test results:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
